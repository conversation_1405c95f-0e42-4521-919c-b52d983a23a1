/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Comic Neue', cursive;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    height: 100vh;
    overflow: hidden;
    user-select: none;
}

.app-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: white;
    border-radius: 12px;
    margin: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

/* Header Styles */
.app-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(90deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
    padding: 12px 20px;
    -webkit-app-region: drag;
    border-bottom: 2px solid #f0f0f0;
}

.app-title {
    color: #333;
    font-size: 24px;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.window-controls {
    display: flex;
    gap: 8px;
    -webkit-app-region: no-drag;
}

.control-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 50%;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
}

.minimize { background: #ffeb3b; color: #333; }
.maximize { background: #4caf50; color: white; }
.close { background: #f44336; color: white; }

.control-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Main Content */
.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* Sidebar Navigation */
.sidebar {
    width: 200px;
    background: linear-gradient(180deg, #a8edea 0%, #fed6e3 100%);
    padding: 20px 0;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 0 25px 25px 0;
    margin-right: 20px;
}

.nav-item:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateX(5px);
}

.nav-item.active {
    background: rgba(255, 255, 255, 0.5);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.nav-icon {
    font-size: 24px;
}

.nav-text {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

/* Content Area */
.content-area {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
    background: #f8f9fa;
}

.content-section {
    display: none;
    animation: fadeIn 0.3s ease-in;
}

.content-section.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Welcome Card */
.welcome-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    margin-bottom: 30px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.welcome-card h2 {
    font-size: 32px;
    margin-bottom: 10px;
}

.welcome-card p {
    font-size: 18px;
    margin-bottom: 30px;
    opacity: 0.9;
}

.quick-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.action-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 20px 30px;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    min-width: 120px;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.action-icon {
    font-size: 32px;
}

/* Games Grid */
.games-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.game-card {
    background: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.game-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

.game-icon {
    font-size: 48px;
    margin-bottom: 15px;
}

.game-card h3 {
    color: #333;
    margin-bottom: 10px;
    font-size: 20px;
}

.game-card p {
    color: #666;
    margin-bottom: 20px;
}

.play-btn {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.play-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 15px rgba(238, 90, 36, 0.4);
}

/* Learning Categories */
.learning-categories {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.category-card {
    background: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

.category-icon {
    font-size: 48px;
    margin-bottom: 15px;
    display: block;
}

/* Creativity Tools */
.creativity-tools {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.tool-card {
    background: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.tool-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

.tool-icon {
    font-size: 48px;
    margin-bottom: 15px;
    display: block;
}

.tool-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    margin-top: 15px;
}

.tool-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 15px rgba(118, 75, 162, 0.4);
}

/* Settings Panel */
.settings-panel {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
}

.setting-group {
    margin-bottom: 30px;
}

.setting-group h3 {
    color: #333;
    margin-bottom: 15px;
    font-size: 18px;
}

.setting-item {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.setting-item label {
    min-width: 80px;
    color: #666;
    font-weight: 600;
}

.setting-item select,
.setting-item input[type="range"] {
    flex: 1;
    padding: 8px 12px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-family: inherit;
}

/* Section Headers */
h2 {
    color: #333;
    font-size: 28px;
    margin-bottom: 10px;
    font-weight: 700;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        width: 60px;
    }
    
    .nav-text {
        display: none;
    }
    
    .nav-item {
        justify-content: center;
        padding: 16px 10px;
    }
    
    .content-area {
        padding: 20px;
    }
    
    .quick-actions {
        flex-direction: column;
        align-items: center;
    }
}
