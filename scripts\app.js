// Main App JavaScript
class KidsApp {
    constructor() {
        this.currentSection = 'home';
        this.init();
    }

    init() {
        this.setupNavigation();
        this.setupWindowControls();
        this.setupQuickActions();
        this.setupGameButtons();
        this.setupToolButtons();
        this.setupSettings();
        this.addAnimations();
    }

    setupNavigation() {
        const navItems = document.querySelectorAll('.nav-item');
        const contentSections = document.querySelectorAll('.content-section');

        navItems.forEach(item => {
            item.addEventListener('click', () => {
                const targetSection = item.dataset.section;
                this.switchSection(targetSection);
                
                // Update active nav item
                navItems.forEach(nav => nav.classList.remove('active'));
                item.classList.add('active');
            });
        });
    }

    switchSection(sectionName) {
        const contentSections = document.querySelectorAll('.content-section');
        
        // Hide all sections
        contentSections.forEach(section => {
            section.classList.remove('active');
        });
        
        // Show target section
        const targetSection = document.getElementById(sectionName);
        if (targetSection) {
            targetSection.classList.add('active');
            this.currentSection = sectionName;
        }
    }

    setupWindowControls() {
        const minimizeBtn = document.getElementById('minimizeBtn');
        const maximizeBtn = document.getElementById('maximizeBtn');
        const closeBtn = document.getElementById('closeBtn');

        if (window.electronAPI) {
            minimizeBtn?.addEventListener('click', () => {
                window.electronAPI.minimize();
            });

            maximizeBtn?.addEventListener('click', () => {
                window.electronAPI.maximize();
            });

            closeBtn?.addEventListener('click', () => {
                window.electronAPI.close();
            });
        }
    }

    setupQuickActions() {
        const actionButtons = document.querySelectorAll('.action-btn');
        
        actionButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const action = btn.dataset.action;
                this.switchSection(action);
                
                // Update nav
                const navItems = document.querySelectorAll('.nav-item');
                navItems.forEach(nav => nav.classList.remove('active'));
                
                const targetNav = document.querySelector(`[data-section="${action}"]`);
                if (targetNav) {
                    targetNav.classList.add('active');
                }
            });
        });
    }

    setupGameButtons() {
        const playButtons = document.querySelectorAll('.play-btn');
        
        playButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const gameCard = e.target.closest('.game-card');
                const gameName = gameCard.querySelector('h3').textContent;
                
                this.showMessage(`Starting ${gameName}! 🎮`, 'success');
                
                // Add some visual feedback
                btn.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    btn.style.transform = '';
                }, 150);
            });
        });
    }

    setupToolButtons() {
        const toolButtons = document.querySelectorAll('.tool-btn');
        
        toolButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const toolCard = e.target.closest('.tool-card');
                const toolName = toolCard.querySelector('h3').textContent;
                
                this.showMessage(`Opening ${toolName} tool! 🎨`, 'success');
                
                // Add some visual feedback
                btn.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    btn.style.transform = '';
                }, 150);
            });
        });
    }

    setupSettings() {
        const themeSelect = document.getElementById('theme');
        const volumeSlider = document.getElementById('volume');

        if (themeSelect) {
            themeSelect.addEventListener('change', (e) => {
                this.changeTheme(e.target.value);
            });
        }

        if (volumeSlider) {
            volumeSlider.addEventListener('input', (e) => {
                this.changeVolume(e.target.value);
            });
        }
    }

    changeTheme(theme) {
        document.body.className = `theme-${theme}`;
        this.showMessage(`Theme changed to ${theme}! ✨`, 'info');
    }

    changeVolume(volume) {
        // Store volume setting
        localStorage.setItem('appVolume', volume);
        console.log(`Volume set to ${volume}%`);
    }

    showMessage(text, type = 'info') {
        // Create a temporary message element
        const message = document.createElement('div');
        message.className = `app-message ${type}`;
        message.textContent = text;
        message.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            background: ${type === 'success' ? '#4caf50' : type === 'error' ? '#f44336' : '#2196f3'};
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            z-index: 1000;
            animation: slideIn 0.3s ease;
        `;

        document.body.appendChild(message);

        // Remove message after 3 seconds
        setTimeout(() => {
            message.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => {
                document.body.removeChild(message);
            }, 300);
        }, 3000);
    }

    addAnimations() {
        // Add hover effects to cards
        const cards = document.querySelectorAll('.game-card, .category-card, .tool-card');
        
        cards.forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-5px) scale(1.02)';
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.transform = '';
            });
        });

        // Add click ripple effect
        const buttons = document.querySelectorAll('button');
        buttons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const ripple = document.createElement('span');
                const rect = btn.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;
                
                ripple.style.cssText = `
                    position: absolute;
                    width: ${size}px;
                    height: ${size}px;
                    left: ${x}px;
                    top: ${y}px;
                    background: rgba(255,255,255,0.3);
                    border-radius: 50%;
                    transform: scale(0);
                    animation: ripple 0.6s ease-out;
                    pointer-events: none;
                `;
                
                btn.style.position = 'relative';
                btn.style.overflow = 'hidden';
                btn.appendChild(ripple);
                
                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });
    }

    // Load saved settings
    loadSettings() {
        const savedVolume = localStorage.getItem('appVolume');
        if (savedVolume) {
            const volumeSlider = document.getElementById('volume');
            if (volumeSlider) {
                volumeSlider.value = savedVolume;
            }
        }
    }
}

// Add CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
    
    @keyframes ripple {
        to { transform: scale(4); opacity: 0; }
    }
`;
document.head.appendChild(style);

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const app = new KidsApp();
    app.loadSettings();
    
    // Show welcome message
    setTimeout(() => {
        app.showMessage('Welcome to Kids App! 🌟', 'success');
    }, 1000);
});

// Handle keyboard shortcuts
document.addEventListener('keydown', (e) => {
    if (e.ctrlKey || e.metaKey) {
        switch(e.key) {
            case '1':
                e.preventDefault();
                document.querySelector('[data-section="home"]').click();
                break;
            case '2':
                e.preventDefault();
                document.querySelector('[data-section="games"]').click();
                break;
            case '3':
                e.preventDefault();
                document.querySelector('[data-section="learning"]').click();
                break;
            case '4':
                e.preventDefault();
                document.querySelector('[data-section="creativity"]').click();
                break;
            case '5':
                e.preventDefault();
                document.querySelector('[data-section="settings"]').click();
                break;
        }
    }
});
